// Professional Timeline Grid - No Scrollbars, Seamless Design
import React, { useMemo } from 'react';
import { Task } from '@/services/api';
import { TimeSlot, GridConfig, YAxisSection } from './types';
import ProfessionalTimelineSection from './ProfessionalTimelineSection';

interface ProfessionalTimelineGridProps {
  timeSlots: TimeSlot[];
  tasks: Task[];
  gridConfig: GridConfig;
  sections: YAxisSection[];
  draggedTask: Task | null;
  zoom: number;
}

const ProfessionalTimelineGrid: React.FC<ProfessionalTimelineGridProps> = ({
  timeSlots,
  gridConfig,
  sections,
  zoom
}) => {
  // Calculate responsive dimensions
  const dimensions = useMemo(() => {
    const baseSlotWidth = 140;
    const baseSectionHeight = 120;
    const sectionLabelWidth = 240;
    
    return {
      slotWidth: baseSlotWidth * zoom,
      sectionHeight: baseSectionHeight,
      sectionLabelWidth,
      totalWidth: sectionLabelWidth + (timeSlots.length * baseSlotWidth * zoom),
      totalHeight: gridConfig.sectionCount * baseSectionHeight
    };
  }, [timeSlots.length, gridConfig.sectionCount, zoom]);

  // Get current time for styling
  const now = new Date();
  const currentHour = now.getHours();

  return (
    <div 
      className="relative bg-white shadow-lg rounded-lg border border-gray-200"
      style={{
        width: `${dimensions.totalWidth}px`,
        height: `${dimensions.totalHeight + 80}px`,
        margin: '20px auto'
      }}
    >
      {/* Professional Time Header */}
      <div className="flex border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
        {/* Section Header */}
        <div 
          className="bg-white border-r border-gray-200 flex items-center justify-center"
          style={{ width: `${dimensions.sectionLabelWidth}px`, height: '80px' }}
        >
          <div className="text-center">
            <h3 className="font-semibold text-gray-800">Sections</h3>
            <p className="text-xs text-gray-500 mt-1">
              {gridConfig.sectionCount} active
            </p>
          </div>
        </div>

        {/* Time Slot Headers */}
        <div className="flex">
          {timeSlots.map(slot => {
            const isPastTime = slot.hour < currentHour;
            const isCurrentTime = slot.hour === currentHour;
            
            return (
              <div
                key={slot.id}
                className={`
                  border-r border-gray-200 flex flex-col items-center justify-center
                  transition-all duration-200
                  ${isPastTime ? 'bg-gray-100 text-gray-500' : 'bg-white text-gray-700'}
                  ${isCurrentTime ? 'bg-blue-50 text-blue-700 font-semibold shadow-inner' : ''}
                `}
                style={{ 
                  width: `${dimensions.slotWidth}px`, 
                  minWidth: `${dimensions.slotWidth}px`,
                  height: '80px'
                }}
              >
                <span className="text-lg font-medium">
                  {slot.label}
                </span>
                <span className="text-xs text-gray-400 mt-1">
                  {slot.hour}:00
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Professional Grid Sections */}
      <div className="relative">
        {sections.slice(0, gridConfig.sectionCount).map((section, index) => (
          <ProfessionalTimelineSection
            key={section.id}
            section={section}
            timeSlots={timeSlots}
            height={dimensions.sectionHeight}
            sectionLabelWidth={dimensions.sectionLabelWidth}
            slotWidth={dimensions.slotWidth}
            isLast={index === gridConfig.sectionCount - 1}
            zoom={zoom}
          />
        ))}
      </div>

      {/* Professional Grid Lines */}
      {gridConfig.showGridLines && (
        <ProfessionalGridLines
          sectionCount={gridConfig.sectionCount}
          timeSlotCount={timeSlots.length}
          dimensions={dimensions}
        />
      )}

      {/* Current Time Indicator */}
      <CurrentTimeIndicator 
        timeSlots={timeSlots} 
        dimensions={dimensions}
      />
    </div>
  );
};

// Professional Grid Lines Component
const ProfessionalGridLines: React.FC<{
  sectionCount: number;
  timeSlotCount: number;
  dimensions: any;
}> = ({ sectionCount, timeSlotCount, dimensions }) => {
  return (
    <div className="absolute inset-0 pointer-events-none z-10">
      {/* Vertical Grid Lines */}
      {Array.from({ length: timeSlotCount }, (_, index) => (
        <div
          key={`vertical-${index}`}
          className="absolute top-0 bottom-0 border-l border-gray-200 opacity-40"
          style={{ 
            left: `${dimensions.sectionLabelWidth + (index + 1) * dimensions.slotWidth}px` 
          }}
        />
      ))}

      {/* Horizontal Grid Lines */}
      {Array.from({ length: sectionCount - 1 }, (_, index) => (
        <div
          key={`horizontal-${index}`}
          className="absolute left-0 right-0 border-t border-gray-200 opacity-40"
          style={{ 
            top: `${80 + (index + 1) * dimensions.sectionHeight}px` 
          }}
        />
      ))}
    </div>
  );
};

// Current Time Indicator Component
const CurrentTimeIndicator: React.FC<{
  timeSlots: TimeSlot[];
  dimensions: any;
}> = ({ timeSlots, dimensions }) => {
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinutes = now.getMinutes();

  const currentSlotIndex = timeSlots.findIndex(slot => slot.hour === currentHour);
  
  if (currentSlotIndex === -1) return null;

  const minutePercentage = currentMinutes / 60;
  const leftPosition = dimensions.sectionLabelWidth + 
    (currentSlotIndex * dimensions.slotWidth) + 
    (minutePercentage * dimensions.slotWidth);

  return (
    <div
      className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-30 pointer-events-none"
      style={{ left: `${leftPosition}px` }}
    >
      {/* Current Time Indicator */}
      <div className="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full border-2 border-white shadow-lg" />
      
      {/* Time Label */}
      <div className="absolute -top-10 -left-8 bg-red-500 text-white text-xs px-3 py-1 rounded-full shadow-lg whitespace-nowrap">
        {now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </div>
    </div>
  );
};

export default ProfessionalTimelineGrid;
