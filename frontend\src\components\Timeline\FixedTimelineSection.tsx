// FIXED Timeline Section - Clean & Working
import React from 'react';
import { YAxisSection, TimeSlot } from './types';
import FixedTimelineSlot from './FixedTimelineSlot';

interface FixedTimelineSectionProps {
  section: YAxisSection;
  timeSlots: TimeSlot[];
  height: number;
  sectionLabelWidth: number;
  slotWidth: number;
  isLast: boolean;
}

const FixedTimelineSection: React.FC<FixedTimelineSectionProps> = ({
  section,
  timeSlots,
  height,
  sectionLabelWidth,
  slotWidth,
  isLast
}) => {
  const now = new Date();
  const currentHour = now.getHours();

  return (
    <div
      className={`flex ${!isLast ? 'border-b border-gray-200' : ''}`}
      style={{ height: `${height}px` }}
    >
      {/* FIXED Section Label */}
      <div 
        className="bg-gray-50 border-r border-gray-200 flex items-center p-4"
        style={{ width: `${sectionLabelWidth}px` }}
      >
        <div className="flex items-center gap-3 w-full">
          {/* Section Color Indicator */}
          {section.color && (
            <div
              className="w-4 h-4 rounded-full flex-shrink-0"
              style={{ backgroundColor: section.color }}
            />
          )}

          {/* Section Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm text-gray-900 truncate">
                {section.name}
              </span>
              <span className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded-full">
                {section.tasks.length}
              </span>
            </div>
            
            {/* Section Type Indicator */}
            <div className="flex items-center gap-2 mt-1">
              <span className="text-xs text-gray-500 capitalize">
                {section.type}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* FIXED Time Slots */}
      <div className="flex">
        {timeSlots.map(slot => (
          <FixedTimelineSlot
            key={slot.id}
            slot={slot}
            section={section}
            tasks={section.tasks}
            slotWidth={slotWidth}
            height={height}
            isCurrentTime={slot.hour === currentHour}
          />
        ))}
      </div>
    </div>
  );
};

export default FixedTimelineSection;
