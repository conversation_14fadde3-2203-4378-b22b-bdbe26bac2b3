// Clean Timeline Slot Component
import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { Plus } from 'lucide-react';
import { Task } from '@/services/api';
import { TimeSlot, YAxisSection, isTaskInSlot } from './types';
import TimelineTask from './TimelineTask';

interface TimelineSlotProps {
  slot: TimeSlot;
  section: YAxisSection;
  tasks: Task[];
  isCurrentTime?: boolean;
}

const TimelineSlot: React.FC<TimelineSlotProps> = ({
  slot,
  section,
  tasks,
  isCurrentTime = false
}) => {
  const { setNodeRef, isOver } = useDroppable({
    id: `${section.id}-${slot.id}`,
    data: { 
      sectionId: section.id, 
      slotId: slot.id,
      hour: slot.hour 
    }
  });

  // Filter tasks that actually belong to this slot
  const slotTasks = tasks.filter(task => isTaskInSlot(task, slot));

  // Check if this is the current time slot
  const now = new Date();
  const isCurrentHour = isCurrentTime && 
    now.getHours() === slot.hour && 
    now.toDateString() === slot.date.toDateString();

  return (
    <div
      ref={setNodeRef}
      className={`
        flex-1 border-r border-gray-100 min-w-[100px] h-full
        transition-all duration-200 relative
        ${isOver ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'}
        ${isCurrentHour ? 'bg-blue-100 border-blue-300' : ''}
      `}
    >
      {/* Current Time Indicator */}
      {isCurrentHour && (
        <div className="absolute top-0 left-0 right-0 h-1 bg-blue-500 z-10" />
      )}

      {/* Slot Content */}
      <div className="p-2 h-full flex flex-col">
        {/* Tasks */}
        <div className="space-y-2 flex-1">
          {slotTasks.map(task => (
            <TimelineTask
              key={task.id}
              task={task}
              section={section}
              isCompact={slotTasks.length > 2}
            />
          ))}
        </div>

        {/* Drop Indicator */}
        {isOver && (
          <div className="border-2 border-dashed border-blue-300 rounded-lg p-3 text-center bg-blue-50">
            <Plus className="h-4 w-4 text-blue-600 mx-auto mb-1" />
            <span className="text-xs text-blue-600 font-medium">
              Drop task here
            </span>
          </div>
        )}

        {/* Empty State */}
        {slotTasks.length === 0 && !isOver && (
          <div className="flex-1 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
            <div className="text-center">
              <Plus className="h-4 w-4 text-gray-300 mx-auto mb-1" />
              <span className="text-xs text-gray-400">
                {slot.label}
              </span>
            </div>
          </div>
        )}

        {/* Task Overflow Indicator */}
        {slotTasks.length > 3 && (
          <div className="mt-2 text-center">
            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
              +{slotTasks.length - 3} more
            </span>
          </div>
        )}
      </div>

      {/* Time Label (for debugging/reference) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute bottom-1 right-1 text-xs text-gray-400">
          {slot.hour}:00
        </div>
      )}
    </div>
  );
};

export default TimelineSlot;
