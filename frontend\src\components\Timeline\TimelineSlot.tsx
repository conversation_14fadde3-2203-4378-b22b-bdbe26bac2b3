// Fixed Timeline Slot Component with Task Extension Support
import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { Plus } from 'lucide-react';
import { Task } from '@/services/api';
import { TimeSlot, YAxisSection, getTaskDurationHours } from './types';
import TimelineTask from './TimelineTask';

interface TimelineSlotProps {
  slot: TimeSlot;
  section: YAxisSection;
  tasks: Task[];
  slotWidth: number;
  isCurrentTime?: boolean;
}

const TimelineSlot: React.FC<TimelineSlotProps> = ({
  slot,
  section,
  tasks,
  slotWidth,
  isCurrentTime = false
}) => {
  const { setNodeRef, isOver } = useDroppable({
    id: `${section.id}-${slot.id}`,
    data: {
      sectionId: section.id,
      slotId: slot.id,
      hour: slot.hour
    }
  });

  // Get current time for styling
  const now = new Date();
  const currentHour = now.getHours();
  const isPastTime = slot.hour < currentHour;
  const isCurrentHour = slot.hour === currentHour;

  // Filter tasks that start in this slot
  const slotTasks = tasks.filter(task => {
    if (!task.scheduledTime) return false;
    const taskDate = new Date(task.scheduledTime);
    return taskDate.getHours() === slot.hour &&
           taskDate.toDateString() === slot.date.toDateString();
  });

  // Get tasks that extend into this slot from previous slots
  const extendedTasks = tasks.filter(task => {
    if (!task.scheduledTime) return false;
    const taskDate = new Date(task.scheduledTime);
    const taskStartHour = taskDate.getHours();
    const taskDuration = getTaskDurationHours(task);
    const taskEndHour = taskStartHour + taskDuration;

    return taskStartHour < slot.hour && taskEndHour > slot.hour &&
           taskDate.toDateString() === slot.date.toDateString();
  });

  return (
    <div
      ref={setNodeRef}
      className={`
        border-r border-gray-200 h-full relative
        transition-all duration-200
        ${isPastTime ? 'bg-gray-100' : 'bg-white'}
        ${isCurrentHour ? 'bg-blue-100 border-blue-300' : ''}
        ${isOver ? 'bg-blue-50 border-blue-400' : 'hover:bg-gray-50'}
      `}
      style={{ width: `${slotWidth}px`, minWidth: `${slotWidth}px` }}
    >
      {/* Current Time Indicator */}
      {isCurrentHour && (
        <div className="absolute top-0 left-0 right-0 h-1 bg-blue-500 z-20" />
      )}

      {/* Slot Content */}
      <div className="p-2 h-full flex flex-col relative">
        {/* Tasks that start in this slot */}
        <div className="space-y-1 flex-1 relative z-10">
          {slotTasks.map((task) => {
            const duration = getTaskDurationHours(task);
            const taskWidth = duration * slotWidth;

            return (
              <div
                key={task.id}
                className="relative"
                style={{
                  width: duration > 1 ? `${taskWidth}px` : '100%',
                  zIndex: 15
                }}
              >
                <TimelineTask
                  task={task}
                  isCompact={slotTasks.length > 2}
                  isExtended={duration > 1}
                  isPastTime={isPastTime}
                />
              </div>
            );
          })}
        </div>

        {/* Extended task indicators (from previous slots) */}
        {extendedTasks.map(task => (
          <div
            key={`extended-${task.id}`}
            className="absolute top-2 left-0 right-0 h-6 bg-gray-200 border border-gray-300 rounded opacity-50 z-5"
          >
            <span className="text-xs text-gray-600 px-2 truncate">
              {task.title} (continued)
            </span>
          </div>
        ))}

        {/* Drop Indicator */}
        {isOver && (
          <div className="absolute inset-2 border-2 border-dashed border-blue-400 rounded-lg bg-blue-50 flex items-center justify-center z-20">
            <div className="text-center">
              <Plus className="h-4 w-4 text-blue-600 mx-auto mb-1" />
              <span className="text-xs text-blue-600 font-medium">
                Drop here
              </span>
            </div>
          </div>
        )}

        {/* Empty State */}
        {slotTasks.length === 0 && extendedTasks.length === 0 && !isOver && (
          <div className="flex-1 flex items-center justify-center opacity-0 hover:opacity-50 transition-opacity">
            <div className="text-center">
              <Plus className="h-3 w-3 text-gray-300 mx-auto mb-1" />
              <span className="text-xs text-gray-400">
                {slot.hour}:00
              </span>
            </div>
          </div>
        )}

        {/* Task Overflow Indicator */}
        {slotTasks.length > 3 && (
          <div className="absolute bottom-2 right-2 z-20">
            <span className="text-xs text-gray-600 bg-white px-2 py-1 rounded-full border shadow-sm">
              +{slotTasks.length - 3}
            </span>
          </div>
        )}
      </div>

      {/* Past Time Overlay */}
      {isPastTime && (
        <div className="absolute inset-0 bg-gray-200 opacity-20 pointer-events-none z-5" />
      )}
    </div>
  );
};

export default TimelineSlot;
