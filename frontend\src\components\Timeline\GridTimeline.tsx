// Professional Timeline - Seamless Design
import React, { useState, useRef, useCallback } from 'react';
import { DndContext } from '@dnd-kit/core';
import { useTimelineState } from './hooks/useTimelineState';
import { useTaskDrop } from './hooks/useTaskDrop';
import TimelineHeader from './TimelineHeader';
import ProfessionalTimelineGrid from './ProfessionalTimelineGrid';
import SeamlessTaskZones from './SeamlessTaskZones';

interface GridTimelineProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

const GridTimeline: React.FC<GridTimelineProps> = ({ currentDate, onDateChange }) => {
  const [isPanning, setIsPanning] = useState(false);
  const [timeOffset, setTimeOffset] = useState(0); // Time-based panning offset
  const [zoom, setZoom] = useState(1);
  const timelineRef = useRef<HTMLDivElement>(null);

  // Clean state management with custom hooks
  const {
    tasks,
    timeSlots,
    gridConfig,
    setGridConfig,
    filteredTasks,
    sections,
    unscheduledTasks,
    pausedTasks,
    completedTasks,
    archivedTasks
  } = useTimelineState(currentDate);

  // Clean drag and drop logic
  const {
    draggedTask,
    handleDragStart,
    handleDragEnd,
    sensors
  } = useTaskDrop(tasks);

  // Fixed pan and zoom handlers - Pan through TIME, not move entire timeline
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.button === 0 && !(e.target as Element).closest('[data-draggable]')) {
      setIsPanning(true);
      e.preventDefault();
    }
  }, []);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isPanning) {
      // Pan through time horizontally (move time slots left/right)
      const timeMovement = e.movementX * 0.5; // Adjust sensitivity
      setTimeOffset(prev => prev + timeMovement);
    }
  }, [isPanning]);

  const handleMouseUp = useCallback(() => {
    setIsPanning(false);
  }, []);

  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setZoom(prev => Math.max(0.5, Math.min(2, prev * delta)));
  }, []);

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="h-screen flex flex-col bg-gradient-to-br from-slate-50 to-white overflow-hidden">
        {/* Professional Header */}
        <TimelineHeader
          currentDate={currentDate}
          onDateChange={onDateChange}
          gridConfig={gridConfig}
          onGridConfigChange={setGridConfig}
        />

        {/* Fixed Timeline Grid - Pan through TIME, not move entire timeline */}
        <div
          ref={timelineRef}
          className={`
            flex-1 relative overflow-hidden
            ${isPanning ? 'cursor-grabbing' : 'cursor-grab'}
          `}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onWheel={handleWheel}
          style={{
            userSelect: isPanning ? 'none' : 'auto'
          }}
        >
          <ProfessionalTimelineGrid
            timeSlots={timeSlots}
            tasks={filteredTasks}
            gridConfig={gridConfig}
            sections={sections}
            draggedTask={draggedTask}
            zoom={zoom}
            timeOffset={timeOffset}
          />
        </div>

        {/* Seamless Task Zones */}
        <SeamlessTaskZones
          unscheduledTasks={unscheduledTasks}
          pausedTasks={pausedTasks}
          completedTasks={completedTasks}
          archivedTasks={archivedTasks}
        />
      </div>
    </DndContext>
  );
};

export default GridTimeline;
