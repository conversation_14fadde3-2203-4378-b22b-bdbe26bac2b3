// Clean Grid Timeline - Main Component
import React from 'react';
import { DndContext } from '@dnd-kit/core';
import { useTimelineState } from './hooks/useTimelineState';
import { useTaskDrop } from './hooks/useTaskDrop';
import TimelineHeader from './TimelineHeader';
import TimelineGrid from './TimelineGrid';
import TaskZones from './TaskZones';

interface GridTimelineProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

const GridTimeline: React.FC<GridTimelineProps> = ({ currentDate, onDateChange }) => {
  // Clean state management with custom hooks
  const {
    tasks,
    timeSlots,
    gridConfig,
    setGridConfig,
    filteredTasks,
    sections,
    unscheduledTasks,
    pausedTasks,
    completedTasks,
    archivedTasks
  } = useTimelineState(currentDate);

  // Clean drag and drop logic
  const {
    draggedTask,
    handleDragStart,
    handleDragEnd,
    sensors
  } = useTaskDrop(tasks);

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="h-full flex flex-col bg-white">
        {/* Clean Header */}
        <TimelineHeader
          currentDate={currentDate}
          onDateChange={onDateChange}
          gridConfig={gridConfig}
          onGridConfigChange={setGridConfig}
        />

        {/* Clean Grid */}
        <TimelineGrid
          timeSlots={timeSlots}
          tasks={filteredTasks}
          gridConfig={gridConfig}
          sections={sections}
          draggedTask={draggedTask}
        />

        {/* Clean Task Zones */}
        <TaskZones
          unscheduledTasks={unscheduledTasks}
          pausedTasks={pausedTasks}
          completedTasks={completedTasks}
          archivedTasks={archivedTasks}
        />
      </div>
    </DndContext>
  );
};

export default GridTimeline;
