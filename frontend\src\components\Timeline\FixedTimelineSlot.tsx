// FIXED Timeline Slot - Working Task Positioning
import React, { useState, useCallback } from 'react';
import { useDroppable } from '@dnd-kit/core';
import { Plus } from 'lucide-react';
import { Task } from '@/services/api';
import { TimeSlot, YAxisSection, getTaskDurationHours } from './types';
import FixedTimelineTask from './FixedTimelineTask';

interface FixedTimelineSlotProps {
  slot: TimeSlot;
  section: YAxisSection;
  tasks: Task[];
  slotWidth: number;
  height: number;
  isCurrentTime?: boolean;
}

const FixedTimelineSlot: React.FC<FixedTimelineSlotProps> = ({
  slot,
  section,
  tasks,
  slotWidth,
  height
}) => {
  const [isHovered, setIsHovered] = useState(false);
  
  const { setNodeRef, isOver } = useDroppable({
    id: `${section.id}-${slot.id}`,
    data: { 
      sectionId: section.id, 
      slotId: slot.id,
      hour: slot.hour 
    }
  });

  // Get current time for styling
  const now = new Date();
  const currentHour = now.getHours();
  const isPastTime = slot.hour < currentHour;
  const isCurrentHour = slot.hour === currentHour;

  // FIXED: Filter tasks that start in this exact slot
  const slotTasks = tasks.filter(task => {
    if (!task.scheduledTime) return false;
    const taskDate = new Date(task.scheduledTime);
    return taskDate.getHours() === slot.hour && 
           taskDate.toDateString() === slot.date.toDateString();
  });

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  return (
    <div
      ref={setNodeRef}
      className={`
        border-r border-gray-200 relative transition-all duration-200
        ${isPastTime ? 'bg-gray-50' : 'bg-white'}
        ${isCurrentHour ? 'bg-blue-50 border-blue-200' : ''}
        ${isOver ? 'bg-blue-100 border-blue-300 shadow-inner' : ''}
        ${isHovered && !isPastTime ? 'bg-gray-50' : ''}
      `}
      style={{ 
        width: `${slotWidth}px`, 
        minWidth: `${slotWidth}px`,
        height: `${height}px`
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Current Time Indicator */}
      {isCurrentHour && (
        <div className="absolute top-0 left-0 right-0 h-1 bg-blue-500 z-20" />
      )}

      {/* FIXED Slot Content */}
      <div className="p-2 h-full flex flex-col relative">
        {/* Tasks that start in this slot */}
        <div className="space-y-1 flex-1 relative z-10">
          {slotTasks.map((task) => {
            const duration = getTaskDurationHours(task);
            
            return (
              <FixedTimelineTask
                key={task.id}
                task={task}
                isCompact={slotTasks.length > 2}
                isExtended={duration > 1}
                isPastTime={isPastTime}
                duration={duration}
                slotWidth={slotWidth}
              />
            );
          })}
        </div>

        {/* Drop Indicator */}
        {isOver && (
          <div className="absolute inset-2 border-2 border-dashed border-blue-400 rounded-lg bg-blue-50 flex items-center justify-center z-20">
            <div className="text-center">
              <Plus className="h-4 w-4 text-blue-600 mx-auto mb-1" />
              <span className="text-xs text-blue-600 font-medium">
                Drop here
              </span>
            </div>
          </div>
        )}

        {/* Empty State */}
        {slotTasks.length === 0 && !isOver && (
          <div className={`
            flex-1 flex items-center justify-center transition-opacity duration-200
            ${isHovered ? 'opacity-50' : 'opacity-0'}
          `}>
            <div className="text-center">
              <Plus className="h-3 w-3 text-gray-300 mx-auto mb-1" />
              <span className="text-xs text-gray-400">
                {slot.hour}:00
              </span>
            </div>
          </div>
        )}

        {/* Task Overflow Indicator */}
        {slotTasks.length > 3 && (
          <div className="absolute bottom-2 right-2 z-20">
            <span className="text-xs text-gray-600 bg-white px-2 py-1 rounded-full border shadow-sm">
              +{slotTasks.length - 3}
            </span>
          </div>
        )}
      </div>

      {/* Past Time Overlay */}
      {isPastTime && (
        <div className="absolute inset-0 bg-gray-200 opacity-20 pointer-events-none z-5" />
      )}
    </div>
  );
};

export default FixedTimelineSlot;
