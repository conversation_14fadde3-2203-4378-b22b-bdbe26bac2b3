// Timeline Types - Clean and Simple
import { Task } from '@/services/api';

export type TaskStatus = 'TODO' | 'IN_PROGRESS' | 'PAUSED' | 'DONE' | 'ARCHIVED';

export interface TimeSlot {
  id: string;
  hour: number;
  date: Date;
  label: string;
}

export interface GridConfig {
  sectionCount: 1 | 2 | 3 | 4 | 5;
  sectionTypes: ('none' | 'projects' | 'tags')[];
  showGridLines: boolean;
  adaptiveNaming: boolean;
}

export interface TimeZone {
  startHour: number;
  endHour: number;
  name: string;
  primaryProject?: string;
  primaryTag?: string;
}

export interface YAxisSection {
  id: string;
  name: string;
  type: 'none' | 'projects' | 'tags';
  tasks: Task[];
  color?: string;
  timeZones: TimeZone[];
}

export interface DragData {
  sectionId: string;
  slotId: string;
  taskId?: string;
}

// Time zone definitions for automatic section naming
export const TIME_ZONES: TimeZone[] = [
  { startHour: 6, endHour: 11, name: 'Morning' },
  { startHour: 12, endHour: 17, name: 'Afternoon' },
  { startHour: 18, endHour: 22, name: 'Evening' },
  { startHour: 23, endHour: 5, name: 'Night' }
];

// Default grid configuration
export const DEFAULT_GRID_CONFIG: GridConfig = {
  sectionCount: 3,
  sectionTypes: ['projects', 'projects', 'none'],
  showGridLines: true,
  adaptiveNaming: true
};

// Helper function to check if task is in a specific time slot
export const isTaskInSlot = (task: Task, slot: TimeSlot): boolean => {
  if (!task.scheduledTime) return false;
  
  const taskDate = new Date(task.scheduledTime);
  const taskHour = taskDate.getHours();
  
  return taskHour === slot.hour && 
         taskDate.toDateString() === slot.date.toDateString();
};

// Helper function to get task duration in hours
export const getTaskDurationHours = (task: Task): number => {
  return Math.ceil((task.effortEstimate || 60) / 60);
};

// Helper function to check if task spans multiple slots
export const getTaskSlotSpan = (task: Task, timeSlots: TimeSlot[]): TimeSlot[] => {
  if (!task.scheduledTime) return [];
  
  const taskDate = new Date(task.scheduledTime);
  const startHour = taskDate.getHours();
  const duration = getTaskDurationHours(task);
  
  const spannedSlots: TimeSlot[] = [];
  
  for (let i = 0; i < duration; i++) {
    const slotHour = startHour + i;
    const slot = timeSlots.find(s => s.hour === slotHour);
    if (slot) {
      spannedSlots.push(slot);
    }
  }
  
  return spannedSlots;
};
