// Clean Timeline Header Component
import React from 'react';
import { 
  Calendar, 
  ChevronLeft, 
  ChevronRight, 
  Settings,
  Grid3X3,
  LayoutGrid
} from 'lucide-react';
import { format, addDays, subDays } from 'date-fns';
import Button from '@/components/ui/Button';
import { GridConfig } from './types';

interface TimelineHeaderProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
  gridConfig: GridConfig;
  onGridConfigChange: (config: GridConfig) => void;
}

const TimelineHeader: React.FC<TimelineHeaderProps> = ({
  currentDate,
  onDateChange,
  gridConfig,
  onGridConfigChange
}) => {
  const handlePreviousDay = () => {
    onDateChange(subDays(currentDate, 1));
  };

  const handleNextDay = () => {
    onDateChange(addDays(currentDate, 1));
  };

  const handleToday = () => {
    onDateChange(new Date());
  };

  const handleSectionCountChange = (count: 1 | 2 | 3 | 4 | 5) => {
    onGridConfigChange({
      ...gridConfig,
      sectionCount: count
    });
  };

  const toggleGridLines = () => {
    onGridConfigChange({
      ...gridConfig,
      showGridLines: !gridConfig.showGridLines
    });
  };

  const toggleAdaptiveNaming = () => {
    onGridConfigChange({
      ...gridConfig,
      adaptiveNaming: !gridConfig.adaptiveNaming
    });
  };

  return (
    <div className="bg-white border-b border-gray-200 p-4">
      <div className="flex items-center justify-between">
        {/* Left: Title and Date Navigation */}
        <div className="flex items-center gap-6">
          <div className="flex items-center gap-3">
            <Calendar className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">Timeline</h1>
          </div>

          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePreviousDay}
              className="p-2"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <div className="flex items-center gap-2">
              <span className="text-lg font-semibold text-gray-900">
                {format(currentDate, 'EEEE, MMMM d')}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleToday}
                className="text-xs"
              >
                Today
              </Button>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleNextDay}
              className="p-2"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Right: Grid Configuration */}
        <div className="flex items-center gap-4">
          {/* Section Count Selector */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">Sections:</span>
            <div className="flex gap-1">
              {[1, 2, 3, 4, 5].map(count => (
                <button
                  key={count}
                  onClick={() => handleSectionCountChange(count as 1 | 2 | 3 | 4 | 5)}
                  className={`
                    w-8 h-8 rounded text-sm font-medium transition-colors
                    ${gridConfig.sectionCount === count
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }
                  `}
                >
                  {count}
                </button>
              ))}
            </div>
          </div>

          {/* Grid Options */}
          <div className="flex items-center gap-2">
            <Button
              variant={gridConfig.showGridLines ? "primary" : "outline"}
              size="sm"
              onClick={toggleGridLines}
              className="flex items-center gap-2"
            >
              <Grid3X3 className="h-4 w-4" />
              Grid Lines
            </Button>

            <Button
              variant={gridConfig.adaptiveNaming ? "primary" : "outline"}
              size="sm"
              onClick={toggleAdaptiveNaming}
              className="flex items-center gap-2"
            >
              <LayoutGrid className="h-4 w-4" />
              Auto Names
            </Button>
          </div>

          {/* Settings Button */}
          <Button
            variant="outline"
            size="sm"
            className="p-2"
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Grid Configuration Details */}
      {!gridConfig.adaptiveNaming && (
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-4">
            <span className="text-sm font-medium text-gray-700">Section Types:</span>
            <div className="flex gap-3">
              {Array.from({ length: gridConfig.sectionCount }, (_, i) => (
                <div key={i} className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Section {i + 1}:</span>
                  <select
                    value={gridConfig.sectionTypes[i] || 'none'}
                    onChange={(e) => {
                      const newTypes = [...gridConfig.sectionTypes];
                      newTypes[i] = e.target.value as 'none' | 'projects' | 'tags';
                      onGridConfigChange({
                        ...gridConfig,
                        sectionTypes: newTypes
                      });
                    }}
                    className="px-2 py-1 text-sm border border-gray-300 rounded"
                  >
                    <option value="none">None</option>
                    <option value="projects">Projects</option>
                    <option value="tags">Tags</option>
                  </select>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TimelineHeader;
