// FIXED Timeline Grid - Actually Working
import React, { useMemo } from 'react';
import { Task } from '@/services/api';
import { TimeSlot, GridConfig, YAxisSection } from './types';
import FixedTimelineSection from './FixedTimelineSection';

interface FixedTimelineGridProps {
  timeSlots: TimeSlot[];
  tasks: Task[];
  gridConfig: GridConfig;
  sections: YAxisSection[];
  draggedTask: Task | null;
  scrollOffset: number;
}

const FixedTimelineGrid: React.FC<FixedTimelineGridProps> = ({
  timeSlots,
  gridConfig,
  sections
}) => {
  // Fixed dimensions - no complex calculations
  const slotWidth = 120;
  const sectionHeight = 100;
  const sectionLabelWidth = 200;
  const totalWidth = sectionLabelWidth + (timeSlots.length * slotWidth);

  // Get current time for styling
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinutes = now.getMinutes();

  // Calculate current time line position
  const currentTimePosition = useMemo(() => {
    const currentSlotIndex = timeSlots.findIndex(slot => slot.hour === currentHour);
    if (currentSlotIndex === -1) return null;

    const minutePercentage = currentMinutes / 60;
    return sectionLabelWidth + (currentSlotIndex * slotWidth) + (minutePercentage * slotWidth);
  }, [timeSlots, currentHour, currentMinutes, sectionLabelWidth, slotWidth]);

  return (
    <div
      className="relative bg-white border border-gray-200 rounded-lg"
      style={{ width: `${totalWidth}px`, minHeight: '400px' }}
    >
      {/* FIXED Time Header */}
      <div className="flex border-b border-gray-200 bg-gray-50 sticky top-0 z-20">
        {/* Section Header */}
        <div
          className="bg-white border-r border-gray-200 flex items-center justify-center"
          style={{ width: `${sectionLabelWidth}px`, height: '60px' }}
        >
          <span className="font-semibold text-gray-700">Sections</span>
        </div>

        {/* Time Slot Headers */}
        <div className="flex">
          {timeSlots.map(slot => {
            const isPastTime = slot.hour < currentHour;
            const isCurrentTime = slot.hour === currentHour;

            return (
              <div
                key={slot.id}
                className={`
                  border-r border-gray-200 flex items-center justify-center
                  ${isPastTime ? 'bg-gray-100 text-gray-500' : 'bg-white text-gray-700'}
                  ${isCurrentTime ? 'bg-blue-100 text-blue-700 font-semibold' : ''}
                `}
                style={{ width: `${slotWidth}px`, height: '60px' }}
              >
                <div className="text-center">
                  <div className="text-sm font-medium">{slot.label}</div>
                  <div className="text-xs text-gray-400">{slot.hour}:00</div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* FIXED Grid Sections */}
      <div className="relative">
        {sections.slice(0, gridConfig.sectionCount).map((section, index) => (
          <FixedTimelineSection
            key={section.id}
            section={section}
            timeSlots={timeSlots}
            height={sectionHeight}
            sectionLabelWidth={sectionLabelWidth}
            slotWidth={slotWidth}
            isLast={index === gridConfig.sectionCount - 1}
          />
        ))}
      </div>

      {/* FIXED Current Time Line */}
      {currentTimePosition && (
        <div
          className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-30 pointer-events-none"
          style={{ left: `${currentTimePosition}px` }}
        >
          {/* Current Time Indicator */}
          <div className="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full border-2 border-white shadow-sm" />

          {/* Time Label */}
          <div className="absolute -top-8 -left-8 bg-red-500 text-white text-xs px-2 py-1 rounded shadow-sm whitespace-nowrap">
            {now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>
      )}

      {/* FIXED Grid Lines */}
      {gridConfig.showGridLines && (
        <div className="absolute inset-0 pointer-events-none z-10">
          {/* Vertical Grid Lines */}
          {timeSlots.map((_, index) => (
            <div
              key={`vertical-${index}`}
              className="absolute top-0 bottom-0 border-l border-gray-200 opacity-30"
              style={{ left: `${sectionLabelWidth + (index + 1) * slotWidth}px` }}
            />
          ))}

          {/* Horizontal Grid Lines */}
          {Array.from({ length: gridConfig.sectionCount - 1 }, (_, index) => (
            <div
              key={`horizontal-${index}`}
              className="absolute left-0 right-0 border-t border-gray-200 opacity-30"
              style={{ top: `${60 + (index + 1) * sectionHeight}px` }}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default FixedTimelineGrid;
