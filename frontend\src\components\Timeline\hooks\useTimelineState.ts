// Clean Timeline State Management
import { useState, useMemo } from 'react';
import { useTaskStore } from '@/store/taskStore';
import { useProjectStore } from '@/store/projectStore';
import { useTagStore } from '@/store/tagStore';
import { 
  GridConfig, 
  TimeSlot, 
  YAxisSection, 
  DEFAULT_GRID_CONFIG,
  TIME_ZONES 
} from '../types';

export const useTimelineState = (currentDate: Date) => {
  const { tasks } = useTaskStore();
  const { projects } = useProjectStore();
  const { tags } = useTagStore();

  // Grid configuration state
  const [gridConfig, setGridConfig] = useState<GridConfig>(DEFAULT_GRID_CONFIG);

  // Generate time slots for the current date
  const timeSlots = useMemo((): TimeSlot[] => {
    const slots: TimeSlot[] = [];
    const startHour = 6; // 6 AM
    const endHour = 23; // 11 PM

    for (let hour = startHour; hour <= endHour; hour++) {
      const slotDate = new Date(currentDate);
      slotDate.setHours(hour, 0, 0, 0);

      slots.push({
        id: `slot-${hour}`,
        hour,
        date: slotDate,
        label: formatHour(hour)
      });
    }

    return slots;
  }, [currentDate]);

  // Filter tasks for current date
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      if (!task.scheduledTime) return false;
      const taskDate = new Date(task.scheduledTime);
      return taskDate.toDateString() === currentDate.toDateString();
    });
  }, [tasks, currentDate]);

  // Generate Y-axis sections based on configuration
  const sections = useMemo((): YAxisSection[] => {
    if (gridConfig.adaptiveNaming) {
      return generateAdaptiveSections(filteredTasks, gridConfig);
    } else {
      return generateManualSections(filteredTasks, gridConfig, projects, tags);
    }
  }, [filteredTasks, gridConfig, projects, tags]);

  // Get unscheduled tasks
  const unscheduledTasks = useMemo(() => {
    return tasks.filter(task => 
      task.status === 'TODO' && !task.scheduledTime
    );
  }, [tasks]);

  // Get paused tasks
  const pausedTasks = useMemo(() => {
    return tasks.filter(task => task.status === 'PAUSED');
  }, [tasks]);

  // Get completed tasks
  const completedTasks = useMemo(() => {
    return tasks.filter(task => task.status === 'DONE');
  }, [tasks]);

  // Get archived tasks
  const archivedTasks = useMemo(() => {
    return tasks.filter(task => task.status === 'ARCHIVED');
  }, [tasks]);

  return {
    tasks,
    timeSlots,
    gridConfig,
    setGridConfig,
    filteredTasks,
    sections,
    projects,
    tags,
    unscheduledTasks,
    pausedTasks,
    completedTasks,
    archivedTasks
  };
};

// Helper function to format hour display
const formatHour = (hour: number): string => {
  if (hour === 0) return '12 AM';
  if (hour === 12) return '12 PM';
  if (hour < 12) return `${hour} AM`;
  return `${hour - 12} PM`;
};

// Generate adaptive sections based on time zones and tasks
const generateAdaptiveSections = (
  tasks: any[], 
  config: GridConfig
): YAxisSection[] => {
  const sections: YAxisSection[] = [];

  TIME_ZONES.forEach((zone, index) => {
    if (index >= config.sectionCount) return;

    const zoneTasks = getTasksInTimeZone(tasks, zone.startHour, zone.endHour);
    const primaryProject = getPrimaryProject(zoneTasks);
    const primaryTag = getPrimaryTag(zoneTasks);

    sections.push({
      id: `zone-${zone.startHour}-${zone.endHour}`,
      name: primaryProject || primaryTag || zone.name,
      type: primaryProject ? 'projects' : primaryTag ? 'tags' : 'none',
      tasks: zoneTasks,
      color: primaryProject ? getProjectColor(primaryProject) : undefined,
      timeZones: [zone]
    });
  });

  // Fill remaining sections if needed
  while (sections.length < config.sectionCount) {
    sections.push({
      id: `section-${sections.length}`,
      name: 'None',
      type: 'none',
      tasks: [],
      timeZones: []
    });
  }

  return sections;
};

// Generate manual sections based on configuration
const generateManualSections = (
  tasks: any[],
  config: GridConfig,
  projects: any[],
  tags: any[]
): YAxisSection[] => {
  const sections: YAxisSection[] = [];

  for (let i = 0; i < config.sectionCount; i++) {
    const sectionType = config.sectionTypes[i] || 'none';
    
    sections.push({
      id: `section-${i}`,
      name: getSectionName(sectionType, i, projects, tags),
      type: sectionType,
      tasks: getSectionTasks(tasks, sectionType, i, projects, tags),
      timeZones: []
    });
  }

  return sections;
};

// Get tasks in a specific time zone
const getTasksInTimeZone = (tasks: any[], startHour: number, endHour: number) => {
  return tasks.filter(task => {
    if (!task.scheduledTime) return false;
    
    const taskDate = new Date(task.scheduledTime);
    const taskHour = taskDate.getHours();
    
    // Handle overnight zones (e.g., 23-5)
    if (startHour > endHour) {
      return taskHour >= startHour || taskHour <= endHour;
    }
    
    return taskHour >= startHour && taskHour <= endHour;
  });
};

// Get primary project from tasks (first scheduled task)
const getPrimaryProject = (tasks: any[]): string | undefined => {
  if (tasks.length === 0) return undefined;
  
  const sortedTasks = tasks
    .filter(task => task.scheduledTime && task.project)
    .sort((a, b) => new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime());
  
  return sortedTasks[0]?.project?.name;
};

// Get primary tag from tasks (first scheduled task)
const getPrimaryTag = (tasks: any[]): string | undefined => {
  if (tasks.length === 0) return undefined;
  
  const sortedTasks = tasks
    .filter(task => task.scheduledTime && task.tag)
    .sort((a, b) => new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime());
  
  return sortedTasks[0]?.tag?.name;
};

// Get project color (placeholder - implement based on your project structure)
const getProjectColor = (projectName: string): string => {
  // This should be implemented based on your project data structure
  return '#3B82F6'; // Default blue
};

// Get section name for manual sections
const getSectionName = (
  type: string, 
  index: number, 
  projects: any[], 
  tags: any[]
): string => {
  switch (type) {
    case 'projects':
      return projects[index]?.name || `Project ${index + 1}`;
    case 'tags':
      return tags[index]?.name || `Tag ${index + 1}`;
    default:
      return `Section ${index + 1}`;
  }
};

// Get tasks for manual sections
const getSectionTasks = (
  tasks: any[],
  type: string,
  index: number,
  projects: any[],
  tags: any[]
): any[] => {
  switch (type) {
    case 'projects':
      const project = projects[index];
      return project ? tasks.filter(task => task.projectId === project.id) : [];
    case 'tags':
      const tag = tags[index];
      return tag ? tasks.filter(task => task.tagId === tag.id) : [];
    default:
      return [];
  }
};
