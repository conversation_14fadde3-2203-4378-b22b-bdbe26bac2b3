# Timeline Grid System - Professional Y-Axis Project/Tag Organization

## 🎯 Overview

This document outlines the complete redesign of the Timeline system with Y-axis grid-style organization based on Projects and Tags. The new system provides precise vertical and horizontal time management with professional drag-and-drop functionality.

## 🔧 Core Requirements

### 1. Grid View Enhancement (NOT a new layout)
- **Y-Axis Grid Lines**: Add vertical grid-style organization
- **Y-Axis Categories**: User-selectable "Tags and/or Projects"
- **Dynamic Sections**: 1-5 configurable sections (default: 3)
- **Section Types**: None, Tags, or Project Names per section
- **Adaptive Sizing**: Elements scale based on section count

### 2. Intelligent Task Placement
- **Project-based Tasks**: Auto-select project vertical section
- **Tagged Tasks**: Auto-select tag+project vertical section
- **Time-based Organization**: Sections change based on time periods
- **Dynamic Naming**: Section names based on first task in time zone

### 3. Time Zone Logic
```
8AM-11AM   → First Task's Project Name
12PM-5PM   → First Task's Project Name
5PM-11PM   → First Task's Project Name
12AM-7AM   → First Task's Project Name
```
- If no tasks in timeframe: Section = "None"

## 🏗️ Technical Architecture

### Grid Configuration State
```typescript
interface GridConfig {
  sectionCount: 1 | 2 | 3 | 4 | 5;
  sectionTypes: ('none' | 'tags' | 'projects')[];
  enableGridLines: boolean;
  adaptiveNaming: boolean;
}

interface TimeZone {
  startHour: number;
  endHour: number;
  primaryProject?: string;
  primaryTag?: string;
  sectionName: string;
}
```

### Y-Axis Section Management
```typescript
interface YAxisSection {
  id: string;
  name: string;
  type: 'none' | 'tags' | 'projects';
  timeZones: TimeZone[];
  tasks: Task[];
  color?: string;
  height: number; // Calculated based on sectionCount
}
```

## 🎨 Visual Design

### Grid Layout Structure
```
┌─────────────────────────────────────────────────────────────┐
│ Timeline Header (Time slots: 8AM, 9AM, 10AM...)            │
├─────────────────────────────────────────────────────────────┤
│ Section 1: [Project A] │ Task │ Task │      │ Task │        │
├─────────────────────────────────────────────────────────────┤
│ Section 2: [Project B] │      │ Task │ Task │      │ Task   │
├─────────────────────────────────────────────────────────────┤
│ Section 3: [None]      │      │      │      │      │        │
└─────────────────────────────────────────────────────────────┘
```

### Section Height Calculation
```typescript
const getSectionHeight = (sectionCount: number): number => {
  const baseHeight = 600; // Total timeline height
  const headerHeight = 80;
  const availableHeight = baseHeight - headerHeight;
  return Math.floor(availableHeight / sectionCount);
};
```

## 🔄 Implementation Plan

### Phase 1: Core Grid Infrastructure
1. **Grid Configuration Component**
   - Section count selector (1-5)
   - Section type dropdowns
   - Grid line toggle
   - Real-time preview

2. **Y-Axis Section Manager**
   - Dynamic section creation
   - Time zone analysis
   - Automatic naming logic
   - Task distribution

### Phase 2: Enhanced Timeline Component
1. **Grid-Enabled Timeline Slots**
   - Multi-row support
   - Section-aware drop zones
   - Vertical grid lines
   - Responsive sizing

2. **Intelligent Task Placement**
   - Project/tag detection
   - Automatic section assignment
   - Time-based reorganization
   - Conflict resolution

### Phase 3: Advanced Features
1. **Dynamic Section Naming**
   - Time zone analysis
   - First task detection
   - Automatic updates
   - Manual override option

2. **Professional Drag & Drop**
   - Section-aware dragging
   - Cross-section movement
   - Visual feedback
   - Precision placement

## 📁 File Structure

### New Components
```
frontend/src/components/Timeline/
├── GridTimeline.tsx              # Main grid-enabled timeline
├── GridConfiguration.tsx         # Grid settings panel
├── YAxisSection.tsx             # Individual Y-axis section
├── GridTimelineSlot.tsx         # Grid-aware time slot
├── SectionManager.tsx           # Section logic handler
└── TimeZoneAnalyzer.tsx         # Time-based organization
```

### Enhanced Components
```
frontend/src/components/Timeline/
├── HorizontalTimeline.tsx       # Updated with grid support
├── TimelineTask.tsx            # Grid-aware task component
└── CollapsibleTaskSection.tsx  # Grid-compatible sections
```

## 🎯 Key Features

### 1. Professional Grid System
- **Visual Grid Lines**: Clean Y-axis organization
- **Adaptive Layout**: Scales with section count
- **Professional Design**: Maintains SimpleLife aesthetics
- **Performance Optimized**: Efficient rendering

### 2. Intelligent Organization
- **Auto-Assignment**: Tasks find correct sections
- **Time-Based Logic**: Sections adapt to schedule
- **Conflict Resolution**: Handles overlapping assignments
- **Manual Override**: User can force placement

### 3. Enhanced User Experience
- **Precise Control**: Exact vertical/horizontal placement
- **Visual Feedback**: Clear section boundaries
- **Smooth Transitions**: Professional animations
- **Touch Support**: Mobile-friendly interactions

## 🔧 Configuration Options

### Section Count (1-5)
- **1 Section**: Single unified timeline
- **2 Sections**: Work/Personal split
- **3 Sections**: Work/Personal/Other (default)
- **4 Sections**: Detailed project separation
- **5 Sections**: Maximum granularity

### Section Types
- **None**: Generic time slots
- **Tags**: Organized by task tags
- **Projects**: Organized by project names

### Grid Appearance
- **Grid Lines**: Toggle Y-axis visual guides
- **Section Labels**: Show/hide section names
- **Adaptive Colors**: Project-based color coding
- **Compact Mode**: Reduced spacing for more sections

## 🚀 Benefits

### For ADHD Users
- **Visual Organization**: Clear project separation
- **Reduced Overwhelm**: Focused sections
- **Precise Planning**: Exact time/project placement
- **Flexible Structure**: Adaptable to needs

### For Time Management
- **Project Focus**: Dedicated project time blocks
- **Context Switching**: Clear project boundaries
- **Time Allocation**: Visual project time distribution
- **Schedule Optimization**: Efficient project scheduling

## 📊 Success Metrics

### Functionality
- ✅ 1-5 configurable sections
- ✅ Dynamic section naming
- ✅ Intelligent task placement
- ✅ Time-based organization
- ✅ Professional grid lines

### Performance
- ✅ Smooth 60fps interactions
- ✅ Efficient section rendering
- ✅ Fast task reorganization
- ✅ Responsive grid updates

### User Experience
- ✅ Intuitive section management
- ✅ Clear visual hierarchy
- ✅ Professional aesthetics
- ✅ ADHD-friendly design

This grid system transforms the Timeline into a powerful project-based time management tool while maintaining the professional drag-and-drop experience users expect.

## 🔨 Detailed Implementation Specifications

### 1. Grid Configuration Component
```typescript
// GridConfiguration.tsx
interface GridConfigurationProps {
  config: GridConfig;
  onConfigChange: (config: GridConfig) => void;
}

const GridConfiguration: React.FC<GridConfigurationProps> = ({ config, onConfigChange }) => {
  return (
    <div className="bg-white border rounded-lg p-4 shadow-sm">
      <h3 className="font-semibold mb-3">Grid Configuration</h3>

      {/* Section Count Selector */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">Sections (1-5)</label>
        <div className="flex gap-2">
          {[1, 2, 3, 4, 5].map(count => (
            <button
              key={count}
              onClick={() => onConfigChange({ ...config, sectionCount: count })}
              className={`px-3 py-2 rounded ${
                config.sectionCount === count
                  ? 'bg-primary-500 text-white'
                  : 'bg-gray-100 hover:bg-gray-200'
              }`}
            >
              {count}
            </button>
          ))}
        </div>
      </div>

      {/* Section Type Selectors */}
      <div className="space-y-3">
        {Array.from({ length: config.sectionCount }, (_, i) => (
          <div key={i} className="flex items-center gap-3">
            <span className="text-sm font-medium w-20">Section {i + 1}:</span>
            <select
              value={config.sectionTypes[i] || 'none'}
              onChange={(e) => {
                const newTypes = [...config.sectionTypes];
                newTypes[i] = e.target.value as 'none' | 'tags' | 'projects';
                onConfigChange({ ...config, sectionTypes: newTypes });
              }}
              className="px-3 py-2 border rounded-md"
            >
              <option value="none">None</option>
              <option value="projects">Projects</option>
              <option value="tags">Tags</option>
            </select>
          </div>
        ))}
      </div>

      {/* Grid Lines Toggle */}
      <div className="mt-4 flex items-center gap-2">
        <input
          type="checkbox"
          id="gridLines"
          checked={config.enableGridLines}
          onChange={(e) => onConfigChange({ ...config, enableGridLines: e.target.checked })}
          className="rounded"
        />
        <label htmlFor="gridLines" className="text-sm font-medium">Show Grid Lines</label>
      </div>
    </div>
  );
};
```

### 2. Y-Axis Section Component
```typescript
// YAxisSection.tsx
interface YAxisSectionProps {
  section: YAxisSection;
  timeSlots: TimeSlot[];
  tasks: Task[];
  height: number;
  onTaskDrop: (taskId: string, sectionId: string, timeSlot: string) => void;
}

const YAxisSection: React.FC<YAxisSectionProps> = ({
  section,
  timeSlots,
  tasks,
  height,
  onTaskDrop
}) => {
  return (
    <div
      className="border-b border-gray-200 bg-white"
      style={{ height: `${height}px` }}
    >
      {/* Section Header */}
      <div className="h-12 bg-gray-50 border-b flex items-center px-4">
        <div className="flex items-center gap-2">
          {section.color && (
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: section.color }}
            />
          )}
          <span className="font-medium text-sm">{section.name}</span>
          <span className="text-xs text-gray-500">({tasks.length})</span>
        </div>
      </div>

      {/* Time Slots Row */}
      <div className="flex h-full">
        {timeSlots.map(slot => (
          <GridTimelineSlot
            key={slot.id}
            slot={slot}
            section={section}
            tasks={tasks.filter(task => isTaskInSlot(task, slot))}
            onTaskDrop={onTaskDrop}
          />
        ))}
      </div>
    </div>
  );
};
```

### 3. Grid Timeline Slot Component
```typescript
// GridTimelineSlot.tsx
interface GridTimelineSlotProps {
  slot: TimeSlot;
  section: YAxisSection;
  tasks: Task[];
  onTaskDrop: (taskId: string, sectionId: string, slotId: string) => void;
}

const GridTimelineSlot: React.FC<GridTimelineSlotProps> = ({
  slot,
  section,
  tasks,
  onTaskDrop
}) => {
  const { setNodeRef, isOver } = useDroppable({
    id: `${section.id}-${slot.id}`,
    data: { sectionId: section.id, slotId: slot.id }
  });

  return (
    <div
      ref={setNodeRef}
      className={`
        flex-1 border-r border-gray-100 p-2 min-h-full
        ${isOver ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'}
      `}
      style={{ minWidth: '120px' }}
    >
      {/* Tasks in this slot */}
      <div className="space-y-1">
        {tasks.map(task => (
          <GridTask
            key={task.id}
            task={task}
            section={section}
            slot={slot}
          />
        ))}
      </div>

      {/* Drop indicator */}
      {isOver && tasks.length === 0 && (
        <div className="border-2 border-dashed border-blue-300 rounded p-2 text-center">
          <span className="text-xs text-blue-600">Drop here</span>
        </div>
      )}
    </div>
  );
};
```

### 4. Time Zone Analyzer
```typescript
// TimeZoneAnalyzer.tsx
export class TimeZoneAnalyzer {
  private static TIME_ZONES = [
    { start: 8, end: 11, name: 'Morning' },
    { start: 12, end: 17, name: 'Afternoon' },
    { start: 17, end: 23, name: 'Evening' },
    { start: 0, end: 7, name: 'Night' }
  ];

  static analyzeSections(tasks: Task[], date: Date): YAxisSection[] {
    const sections: YAxisSection[] = [];

    this.TIME_ZONES.forEach(zone => {
      const zoneTasks = this.getTasksInTimeZone(tasks, date, zone.start, zone.end);
      const primaryProject = this.getPrimaryProject(zoneTasks);
      const primaryTag = this.getPrimaryTag(zoneTasks);

      sections.push({
        id: `zone-${zone.start}-${zone.end}`,
        name: primaryProject || primaryTag || 'None',
        type: primaryProject ? 'projects' : primaryTag ? 'tags' : 'none',
        timeZones: [{
          startHour: zone.start,
          endHour: zone.end,
          primaryProject,
          primaryTag,
          sectionName: primaryProject || primaryTag || 'None'
        }],
        tasks: zoneTasks,
        color: primaryProject ? this.getProjectColor(primaryProject) : undefined,
        height: 150 // Will be calculated dynamically
      });
    });

    return sections;
  }

  private static getTasksInTimeZone(tasks: Task[], date: Date, startHour: number, endHour: number): Task[] {
    return tasks.filter(task => {
      if (!task.scheduledTime) return false;

      const taskDate = new Date(task.scheduledTime);
      const taskHour = taskDate.getHours();

      // Handle overnight zones (e.g., 23-7)
      if (startHour > endHour) {
        return taskHour >= startHour || taskHour <= endHour;
      }

      return taskHour >= startHour && taskHour <= endHour;
    });
  }

  private static getPrimaryProject(tasks: Task[]): string | undefined {
    if (tasks.length === 0) return undefined;

    // Get the project of the first scheduled task in the time zone
    const sortedTasks = tasks
      .filter(task => task.scheduledTime)
      .sort((a, b) => new Date(a.scheduledTime!).getTime() - new Date(b.scheduledTime!).getTime());

    return sortedTasks[0]?.project?.name;
  }

  private static getPrimaryTag(tasks: Task[]): string | undefined {
    if (tasks.length === 0) return undefined;

    const sortedTasks = tasks
      .filter(task => task.scheduledTime && task.tag)
      .sort((a, b) => new Date(a.scheduledTime!).getTime() - new Date(b.scheduledTime!).getTime());

    return sortedTasks[0]?.tag?.name;
  }

  private static getProjectColor(projectName: string): string {
    // Implementation to get project color
    return '#3B82F6'; // Default blue
  }
}
```

### 5. Main Grid Timeline Component
```typescript
// GridTimeline.tsx
interface GridTimelineProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

const GridTimeline: React.FC<GridTimelineProps> = ({ currentDate, onDateChange }) => {
  const { tasks, updateTask } = useTaskStore();
  const { projects } = useProjectStore();
  const { tags } = useTagStore();

  // Grid configuration state
  const [gridConfig, setGridConfig] = useState<GridConfig>({
    sectionCount: 3,
    sectionTypes: ['projects', 'projects', 'none'],
    enableGridLines: true,
    adaptiveNaming: true
  });

  // Generate sections based on configuration
  const sections = useMemo(() => {
    if (gridConfig.adaptiveNaming) {
      return TimeZoneAnalyzer.analyzeSections(tasks, currentDate);
    } else {
      return generateManualSections(gridConfig, tasks, projects, tags);
    }
  }, [tasks, currentDate, gridConfig, projects, tags]);

  // Calculate section heights
  const sectionHeight = useMemo(() => {
    return getSectionHeight(gridConfig.sectionCount);
  }, [gridConfig.sectionCount]);

  // Generate time slots for the current date
  const timeSlots = useMemo(() => {
    return generateTimeSlots(currentDate, '1hr'); // Default to 1-hour slots
  }, [currentDate]);

  const handleTaskDrop = useCallback((taskId: string, sectionId: string, slotId: string) => {
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;

    // Calculate new scheduled time based on slot
    const slot = timeSlots.find(s => s.id === slotId);
    if (!slot) return;

    // Update task with new time and section assignment
    updateTask(taskId, {
      scheduledTime: slot.date.toISOString(),
      status: 'IN_PROGRESS' as TaskStatus
    });
  }, [tasks, timeSlots, updateTask]);

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header with Grid Configuration */}
      <div className="border-b bg-gray-50 p-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">Grid Timeline</h2>
          <GridConfiguration
            config={gridConfig}
            onConfigChange={setGridConfig}
          />
        </div>
      </div>

      {/* Time Header */}
      <div className="border-b bg-white sticky top-0 z-10">
        <div className="flex">
          <div className="w-48 border-r bg-gray-50 p-4">
            <span className="font-medium">Sections</span>
          </div>
          <div className="flex flex-1">
            {timeSlots.map(slot => (
              <div
                key={slot.id}
                className="flex-1 border-r border-gray-100 p-2 text-center min-w-[120px]"
              >
                <div className="text-sm font-medium">
                  {format(slot.date, 'ha')}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Grid Sections */}
      <div className="flex-1 overflow-auto">
        {sections.slice(0, gridConfig.sectionCount).map(section => (
          <YAxisSection
            key={section.id}
            section={section}
            timeSlots={timeSlots}
            tasks={section.tasks}
            height={sectionHeight}
            onTaskDrop={handleTaskDrop}
          />
        ))}
      </div>

      {/* Grid Lines Overlay */}
      {gridConfig.enableGridLines && (
        <div className="absolute inset-0 pointer-events-none">
          <GridLinesOverlay
            sectionCount={gridConfig.sectionCount}
            timeSlotCount={timeSlots.length}
          />
        </div>
      )}
    </div>
  );
};
```

## 🔄 Migration Strategy

### Phase 1: Preserve Existing Timeline (90% of current code)
- Keep `HorizontalTimeline.tsx` as fallback
- Add grid toggle in timeline header
- Maintain all existing drag-and-drop functionality
- Preserve task zones and collapsible sections

### Phase 2: Integrate Grid System (10% new code)
- Add `GridTimeline.tsx` as alternative view
- Implement grid configuration panel
- Add Y-axis section management
- Integrate time zone analyzer

### Phase 3: Seamless Transition
- Add view toggle button in timeline header
- Sync state between grid and horizontal views
- Maintain user preferences
- Ensure 100% feature parity

This approach ensures we maintain the existing professional timeline while adding the powerful grid system as an enhancement, not a replacement.
