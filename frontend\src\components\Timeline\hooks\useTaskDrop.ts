// Clean Drag and Drop Logic
import { useState, useCallback } from 'react';
import {
  DragStartEvent,
  DragEndEvent,
  PointerSensor,
  useSensor,
  useSensors
} from '@dnd-kit/core';
import { Task } from '@/services/api';
import { useTaskStore } from '@/store/taskStore';
import { DragData } from '../types';

export const useTaskDrop = (tasks: Task[]) => {
  const { updateTask } = useTaskStore();
  const [draggedTask, setDraggedTask] = useState<Task | null>(null);

  // Configure sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3, // 3px movement to start drag
      },
    })
  );

  // Handle drag start
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const task = tasks.find(t => t.id === event.active.id);
    setDraggedTask(task || null);
  }, [tasks]);

  // Handle drag end
  const handleDragEnd = useCallback(async (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedTask(null);

    if (!over || !active) return;

    const taskId = active.id as string;
    const dropZoneId = over.id as string;
    const dropData = over.data?.current as DragData;

    try {
      // Handle different drop zones
      if (dropZoneId.includes('unscheduled-section')) {
        // Dropped in unscheduled zone
        await updateTask(taskId, {
          status: 'TODO',
          scheduledTime: undefined
        });
      } else if (dropZoneId.includes('paused-section')) {
        // Dropped in paused zone
        await updateTask(taskId, {
          status: 'PAUSED',
          scheduledTime: undefined
        });
      } else if (dropZoneId.includes('completed-section')) {
        // Dropped in completed zone
        await updateTask(taskId, {
          status: 'DONE'
        });
      } else if (dropZoneId.includes('archived-section')) {
        // Dropped in archived zone
        await updateTask(taskId, {
          status: 'ARCHIVED'
        });
      } else if (dropData?.sectionId && dropData?.slotId) {
        // Dropped in timeline grid
        await handleTimelineSlotDrop(taskId, dropData);
      }
    } catch (error) {
      console.error('Failed to update task:', error);
      // TODO: Show error notification
    }
  }, [updateTask]);

  // Handle timeline slot drop
  const handleTimelineSlotDrop = async (taskId: string, dropData: DragData) => {
    const { sectionId, slotId } = dropData;

    // Extract hour from slot ID or use hour from dropData
    let hour: number;
    if (dropData.hour !== undefined) {
      hour = dropData.hour;
    } else {
      hour = parseInt(slotId.replace('slot-', ''));
      if (isNaN(hour)) return;
    }

    // Create new scheduled time for today
    const scheduledDate = new Date();
    scheduledDate.setHours(hour, 0, 0, 0);

    // Update task with new schedule and status
    await updateTask(taskId, {
      scheduledTime: scheduledDate.toISOString(),
      status: 'IN_PROGRESS'
    });

    // Handle section assignment based on sectionId
    await handleSectionAssignment(taskId, sectionId);
  };

  // Handle section assignment (project/tag assignment)
  const handleSectionAssignment = async (_taskId: string, sectionId: string) => {
    // Extract section type and index from sectionId
    if (sectionId.startsWith('zone-')) {
      // Adaptive section - no additional assignment needed
      return;
    }

    if (sectionId.startsWith('section-')) {
      // Manual section - could assign project or tag
      // This would need to be implemented based on section configuration
      // For now, we'll leave the existing project/tag assignments
      return;
    }
  };

  return {
    draggedTask,
    handleDragStart,
    handleDragEnd,
    sensors
  };
};
