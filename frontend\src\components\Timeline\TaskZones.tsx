import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { Clock, Pause, CheckCircle2, Archive } from 'lucide-react';
import { Task } from '@/services/api';
import TagBadge from '@/components/Tags/TagBadge';

interface TaskZoneProps {
  id: string;
  title: string;
  icon: React.ReactNode;
  tasks: Task[];
  color: string;
  description: string;
  onTaskClick?: (task: Task) => void;
}

const TaskZone: React.FC<TaskZoneProps> = ({
  id,
  title,
  icon,
  tasks,
  color,
  description,
  onTaskClick,
}) => {
  const { setNodeRef, isOver } = useDroppable({
    id,
  });

  return (
    <div
      ref={setNodeRef}
      className={`
        bg-white border-2 rounded-lg p-4 transition-all min-h-[200px]
        ${isOver ? `border-${color}-400 bg-${color}-50` : 'border-secondary-200 hover:border-secondary-300'}
      `}
    >
      {/* Zone Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className={`p-2 rounded-lg bg-${color}-100 text-${color}-600`}>
            {icon}
          </div>
          <div>
            <h3 className="font-semibold text-secondary-900">{title}</h3>
            <p className="text-xs text-secondary-600">{description}</p>
          </div>
        </div>
        <span className={`px-2 py-1 rounded-full text-xs font-medium bg-${color}-100 text-${color}-700`}>
          {tasks.length}
        </span>
      </div>

      {/* Drop Indicator */}
      {isOver && (
        <div className={`border-2 border-dashed border-${color}-400 rounded-lg p-4 mb-3 bg-${color}-50`}>
          <p className={`text-center text-sm text-${color}-600 font-medium`}>
            Drop task here
          </p>
        </div>
      )}

      {/* Tasks */}
      <div className="space-y-2">
        {tasks.map((task) => (
          <div
            key={task.id}
            onClick={() => onTaskClick?.(task)}
            className="p-3 bg-secondary-50 rounded-lg border border-secondary-200 hover:border-secondary-300 cursor-pointer transition-colors"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-secondary-900 truncate">
                  {task.title}
                </h4>
                {task.content && (
                  <p className="text-xs text-secondary-600 mt-1 line-clamp-2">
                    {task.content}
                  </p>
                )}
                <div className="flex items-center gap-2 mt-2">
                  {task.project && (
                    <span
                      className="inline-block w-2 h-2 rounded-full"
                      style={{ backgroundColor: task.project.color || '#64748b' }}
                      title={task.project.name}
                    />
                  )}
                  {task.tag && (
                    <TagBadge tag={task.tag} size="sm" />
                  )}
                  {task.scheduledTime && (
                    <span className="text-xs text-secondary-500">
                      {new Date(task.scheduledTime).toLocaleTimeString('en-US', {
                        hour: 'numeric',
                        minute: '2-digit',
                      })}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}

        {tasks.length === 0 && !isOver && (
          <div className="text-center py-8 text-secondary-400">
            <div className={`w-12 h-12 mx-auto mb-2 rounded-full bg-${color}-100 flex items-center justify-center`}>
              {icon}
            </div>
            <p className="text-sm">No tasks in this zone</p>
          </div>
        )}
      </div>
    </div>
  );
};

interface TaskZonesProps {
  tasks: Task[];
  onTaskClick?: (task: Task) => void;
}

const TaskZones: React.FC<TaskZonesProps> = ({ tasks, onTaskClick }) => {
  // Categorize tasks by status
  const unscheduledTasks = tasks.filter(task => 
    task.status === 'TODO' && !task.scheduledTime
  );
  
  const pausedTasks = tasks.filter(task => 
    task.status === 'PAUSED'
  );
  
  const completedTasks = tasks.filter(task => 
    task.status === 'DONE'
  );
  
  const archivedTasks = tasks.filter(task => 
    task.status === 'ARCHIVED'
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Not Scheduled Zone */}
      <TaskZone
        id="not-scheduled"
        title="Not Scheduled"
        icon={<Clock className="h-4 w-4" />}
        tasks={unscheduledTasks}
        color="blue"
        description="Tasks waiting to be scheduled"
        onTaskClick={onTaskClick}
      />

      {/* Paused Zone */}
      <TaskZone
        id="paused"
        title="Paused"
        icon={<Pause className="h-4 w-4" />}
        tasks={pausedTasks}
        color="yellow"
        description="Tasks temporarily paused"
        onTaskClick={onTaskClick}
      />

      {/* Completed Zone */}
      <TaskZone
        id="completed"
        title="Completed"
        icon={<CheckCircle2 className="h-4 w-4" />}
        tasks={completedTasks}
        color="green"
        description="Finished tasks"
        onTaskClick={onTaskClick}
      />

      {/* Archived Zone */}
      <TaskZone
        id="archived"
        title="Archived"
        icon={<Archive className="h-4 w-4" />}
        tasks={archivedTasks}
        color="gray"
        description="Archived tasks"
        onTaskClick={onTaskClick}
      />
    </div>
  );
};

export default TaskZones;
