// Clean Timeline Grid Component
import React from 'react';
import { Task } from '@/services/api';
import { TimeSlot, GridConfig, YAxisSection } from './types';
import TimelineSection from './TimelineSection';

interface TimelineGridProps {
  timeSlots: TimeSlot[];
  tasks: Task[];
  gridConfig: GridConfig;
  sections: YAxisSection[];
  draggedTask: Task | null;
}

const TimelineGrid: React.FC<TimelineGridProps> = ({
  timeSlots,
  gridConfig,
  sections
}) => {
  // Calculate section height based on available space
  const sectionHeight = Math.floor(500 / gridConfig.sectionCount);

  return (
    <div className="flex-1 flex flex-col border border-gray-200 relative">
      {/* Time Header */}
      <div className="flex border-b bg-gray-50 sticky top-0 z-20">
        {/* Section Header */}
        <div className="w-48 p-3 border-r bg-white flex items-center">
          <span className="font-medium text-gray-700">Sections</span>
        </div>

        {/* Time Slot Headers */}
        <div className="flex flex-1">
          {timeSlots.map(slot => (
            <div
              key={slot.id}
              className="flex-1 p-3 text-center border-r border-gray-100 min-w-[100px]"
            >
              <span className="text-sm font-medium text-gray-700">
                {slot.label}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Grid Sections */}
      <div className="flex-1 overflow-auto">
        {sections.slice(0, gridConfig.sectionCount).map((section, index) => (
          <TimelineSection
            key={section.id}
            section={section}
            timeSlots={timeSlots}
            height={sectionHeight}
            isLast={index === gridConfig.sectionCount - 1}
          />
        ))}
      </div>

      {/* Grid Lines Overlay */}
      {gridConfig.showGridLines && (
        <GridLinesOverlay
          sectionCount={gridConfig.sectionCount}
          timeSlotCount={timeSlots.length}
          sectionHeight={sectionHeight}
        />
      )}

      {/* Current Time Line */}
      <CurrentTimeLine timeSlots={timeSlots} />
    </div>
  );
};

// Grid Lines Overlay Component
const GridLinesOverlay: React.FC<{
  sectionCount: number;
  timeSlotCount: number;
  sectionHeight: number;
}> = ({ sectionCount, timeSlotCount, sectionHeight }) => {
  return (
    <div className="absolute inset-0 pointer-events-none z-10">
      {/* Vertical Grid Lines */}
      {Array.from({ length: timeSlotCount }, (_, index) => (
        <div
          key={`vertical-${index}`}
          className="absolute top-0 bottom-0 border-l border-gray-200 opacity-50"
          style={{
            left: `${192 + (index + 1) * (100 / timeSlotCount)}%`
          }}
        />
      ))}

      {/* Horizontal Grid Lines */}
      {Array.from({ length: sectionCount - 1 }, (_, index) => (
        <div
          key={`horizontal-${index}`}
          className="absolute left-0 right-0 border-t border-gray-200 opacity-50"
          style={{
            top: `${60 + (index + 1) * sectionHeight}px`
          }}
        />
      ))}
    </div>
  );
};

// Current Time Line Component
const CurrentTimeLine: React.FC<{
  timeSlots: TimeSlot[];
}> = ({ timeSlots }) => {
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinutes = now.getMinutes();

  // Find the current time slot
  const currentSlotIndex = timeSlots.findIndex(slot => slot.hour === currentHour);

  if (currentSlotIndex === -1) return null;

  // Calculate position within the hour (0-100%)
  const minutePercentage = currentMinutes / 60;
  const slotWidth = 100 / timeSlots.length;
  const leftPosition = 192 + (currentSlotIndex * slotWidth) + (minutePercentage * slotWidth);

  return (
    <div
      className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-30 pointer-events-none"
      style={{ left: `${leftPosition}%` }}
    >
      {/* Current Time Indicator */}
      <div className="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full border-2 border-white shadow-sm" />

      {/* Time Label */}
      <div className="absolute -top-8 -left-8 bg-red-500 text-white text-xs px-2 py-1 rounded shadow-sm">
        {now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </div>
    </div>
  );
};

export default TimelineGrid;
