// Clean Timeline Section Component
import React from 'react';
import { YAxisSection, TimeSlot } from './types';
import TimelineSlot from './TimelineSlot';

interface TimelineSectionProps {
  section: YAxisSection;
  timeSlots: TimeSlot[];
  height: number;
  isLast: boolean;
}

const TimelineSection: React.FC<TimelineSectionProps> = ({
  section,
  timeSlots,
  height,
  isLast
}) => {
  // Get current time for highlighting current slot
  const now = new Date();
  const currentHour = now.getHours();

  return (
    <div
      className={`flex ${!isLast ? 'border-b border-gray-200' : ''}`}
      style={{ height: `${height}px` }}
    >
      {/* Section Label */}
      <div className="w-48 bg-gray-50 border-r border-gray-200 flex items-center p-4">
        <div className="flex items-center gap-3 w-full">
          {/* Section Color Indicator */}
          {section.color && (
            <div
              className="w-4 h-4 rounded-full flex-shrink-0"
              style={{ backgroundColor: section.color }}
            />
          )}

          {/* Section Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm text-gray-900 truncate">
                {section.name}
              </span>
              <span className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded-full">
                {section.tasks.length}
              </span>
            </div>
            
            {/* Section Type Indicator */}
            <div className="flex items-center gap-2 mt-1">
              <span className="text-xs text-gray-500 capitalize">
                {section.type}
              </span>
              
              {/* Time Zone Info for Adaptive Sections */}
              {section.timeZones.length > 0 && (
                <span className="text-xs text-gray-400">
                  {section.timeZones.map(tz => 
                    `${tz.startHour}:00-${tz.endHour}:00`
                  ).join(', ')}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Time Slots */}
      <div className="flex flex-1">
        {timeSlots.map(slot => (
          <TimelineSlot
            key={slot.id}
            slot={slot}
            section={section}
            tasks={section.tasks}
            isCurrentTime={slot.hour === currentHour}
          />
        ))}
      </div>
    </div>
  );
};

export default TimelineSection;
